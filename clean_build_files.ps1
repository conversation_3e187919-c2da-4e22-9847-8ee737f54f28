# C语言嵌入式项目编译中间文件清理脚本
# 用于清理Nordic SDK项目中的编译中间文件

Write-Host "开始清理C语言嵌入式项目编译中间文件..." -ForegroundColor Green

# 定义要清理的文件扩展名
$extensions = @(
    "*.o", "*.obj",           # 目标文件
    "*.hex", "*.bin", "*.elf", # 可执行文件
    "*.map", "*.lst", "*.sym", # 调试信息文件
    "*.a", "*.lib",           # 库文件
    "*.d",                    # 依赖文件
    "*.su",                   # 栈使用文件
    "*.tmp", "*.temp"         # 临时文件
)

# 定义要清理的目录
$directories = @(
    "_build", "build", "Build", "BUILD",
    "Debug", "Release", "obj", "bin",
    ".vs", ".vscode/ipch"
)

$totalDeleted = 0

# 清理文件
foreach ($ext in $extensions) {
    Write-Host "清理文件类型: $ext" -ForegroundColor Yellow
    $files = Get-ChildItem -Path . -Recurse -Name $ext -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        try {
            Remove-Item $file -Force
            $totalDeleted++
            Write-Host "  删除: $file" -ForegroundColor Gray
        }
        catch {
            Write-Host "  无法删除: $file - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 清理目录
foreach ($dir in $directories) {
    Write-Host "清理目录: $dir" -ForegroundColor Yellow
    $dirs = Get-ChildItem -Path . -Recurse -Directory -Name $dir -ErrorAction SilentlyContinue
    foreach ($directory in $dirs) {
        try {
            Remove-Item $directory -Recurse -Force
            $totalDeleted++
            Write-Host "  删除目录: $directory" -ForegroundColor Gray
        }
        catch {
            Write-Host "  无法删除目录: $directory - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "清理完成！共删除 $totalDeleted 个文件/目录" -ForegroundColor Green
Write-Host "建议运行 'git status' 检查清理效果" -ForegroundColor Cyan
