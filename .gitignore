# C语言嵌入式项目 .gitignore 文件

# =============================================================================
# 编译产物和目标文件
# =============================================================================

# 目标文件
*.o
*.obj
*.elf
*.axf
*.out
*.bin
*.hex
*.map
*.lst
*.lss

# 库文件
*.a
*.lib
*.so
*.dll
*.dylib

# 可执行文件
*.exe
*.app

# 调试信息文件
*.pdb
*.dSYM/
*.su
*.idb
*.ipdb

# =============================================================================
# 编译器和IDE特定文件
# =============================================================================

# Keil uVision
*.uvguix.*
*.uvoptx
*.uvprojx.bak
*.scvd
*.dep
*.crf
*.d
*.htm
*.lnp
*.sct
*.Bak
*.plg
*.tra
*.fed
*.l1p
*.l2p
*.iex
*.jev

# IAR Embedded Workbench
*.pbd
*.pbi
*.pbi.*
*.pdb
*.xcl
*.map
*.html
*.tmp
*.TMP
*.o
*.hi
*.obj
*.out
*.list
*.lst
*.ewd
*.eww
*.ewp
*.dep
*.d
*.browse
*.tmp
*.o
*.hi
*.obj
*.out
*.list
*.lst

# GCC/Make
*.d
*.o
*.a
*.so
*.map
*.elf
*.bin
*.hex
*.srec
*.list
*.lss
*.sym
*.tmp

# =============================================================================
# 构建目录
# =============================================================================

# 通用构建目录
build/
Build/
_build/
*/_build/
*/build/
*/Build/
output/
Output/
bin/
Bin/
obj/
Obj/
Debug/
Release/
dist/

# =============================================================================
# 调试器和仿真器文件
# =============================================================================

# J-Link
JLinkLog.txt
JLinkSettings.ini
JLink.log

# ST-Link
*.jdebug
*.jdebug.user

# OpenOCD
openocd.log

# GDB
.gdbinit
*.gdb

# =============================================================================
# IDE和编辑器文件
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# Visual Studio
*.vcxproj.user
*.vcxproj.filters
*.sln.docstates
*.suo
*.user
*.userosscache
*.sln.docstates

# Eclipse
.metadata
.recommenders/
.project
.cproject
.settings/

# Code::Blocks
*.cbp
*.layout
*.depend

# Dev-C++
*.dev

# Qt Creator
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
*.moc.cpp
*.qm
*.prl

# =============================================================================
# 操作系统文件
# =============================================================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# 临时文件和缓存
# =============================================================================

# 通用临时文件
*.tmp
*.temp
*.log
*.bak
*.swp
*.swo
*~
.#*
\#*#

# 编译器缓存
*.gch
*.pch

# =============================================================================
# 文档和自动生成文件
# =============================================================================

# Doxygen
html/
latex/
*.tag

# =============================================================================
# 版本控制
# =============================================================================

# Git
*.orig
*.rej

# SVN
.svn/

# =============================================================================
# 包管理器
# =============================================================================

# Conan
conanfile.txt.user
conaninfo.txt
conanbuildinfo.*

# vcpkg
vcpkg_installed/

# =============================================================================
# 其他
# =============================================================================

# 压缩文件
*.zip
*.rar
*.7z
*.tar
*.gz
*.bz2
*.xz

# 备份文件
*.backup
*.old

# 测试覆盖率
*.gcov
*.gcda
*.gcno
coverage/

# 性能分析
*.prof
gmon.out

# 内存检查
*.memcheck
valgrind-*.log

# =============================================================================
# Nordic SDK 特定文件
# =============================================================================

# Nordic SDK 构建目录
*/_build/
*/build/
**/pca*/
**/arm*/
**/gcc*/
**/iar*/
**/ses*/

# Nordic 特定的编译输出
*.jlink
*.flash
*.uvguix
*.uvoptx
*.uvprojx
*.scvd

# SoftDevice 和 bootloader（如果不需要版本控制）
# 注释掉下面的行如果你需要版本控制这些文件
# *.hex
# **/hex/

# Nordic 工具链文件
*.jdebug*
*.emProject
*.emSession
